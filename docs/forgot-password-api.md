# Forgot Password API Documentation

## Overview

The Forgot Password API provides a secure way for users to reset their passwords using Azure B2C authentication. When a user requests a password reset, the system sends an email with a secure B2C password reset link that allows them to set a new password.

## API Endpoint

### POST /auth/forgot-password

Initiates a password reset process for a user account.

**URL:** `POST /auth/forgot-password`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "If an account with this email exists, you will receive a password reset link shortly."
}
```

**Status Codes:**
- `200 OK` - Request processed successfully (always returned for security)
- `400 Bad Request` - Invalid request (missing email or invalid format)

## Security Features

### Email Enumeration Protection
- The API always returns a success message, regardless of whether the email exists
- This prevents attackers from determining which email addresses are registered
- Error details are logged server-side but not exposed to clients

### Rate Limiting
- Consider implementing rate limiting to prevent abuse
- Recommended: 5 requests per IP per 15 minutes

### Input Validation
- Email format validation using regex
- Sanitization of input parameters

## Flow Description

1. **User Request**: User submits their email address
2. **Validation**: System validates email format and checks if user exists
3. **User Lookup**: System queries the database for the user account
4. **Security Check**: System verifies the account is active
5. **B2C URL Generation**: System generates a secure B2C password reset URL with PKCE parameters
6. **Email Sending**: System sends a professional email with the reset link
7. **Audit Logging**: System logs the password reset request for security auditing

## B2C Integration

### URL Generation
The system generates Azure B2C URLs with the following parameters:
- **PKCE Challenge**: Secure code challenge for OAuth flow
- **Nonce**: Unique identifier for security
- **State**: Session state management
- **Login Hint**: Pre-fills the email in B2C form
- **Redirect URI**: Different URLs for admin vs regular users

### Policy Configuration
- Uses `signin_policy` from environment variables
- Falls back to password reset policy if configured
- Supports both admin and user flows

## Email Template

### Content Structure
- Professional EMR Platform branding
- Clear call-to-action button
- Security warnings and best practices
- Fallback text link for accessibility
- Different templates for admin vs regular users

### Security Warnings Included
- Link expiration notice (24 hours)
- Instructions for users who didn't request reset
- Account security assurance

## Environment Variables

Required environment variables:
```
TENANT_NAME=erm20240520
CLIENT_ID=your-b2c-client-id
signin_policy=B2C_1_emrapp
BASE_URL=https://arca-emr.vercel.app
BASE_ADMIN_URL=https://arca-ai-admin.vercel.app
EMAIL_USER=your-smtp-email
EMAIL_PASSWORD=your-smtp-password
JWT_SECRET=your-jwt-secret
```

Optional:
```
password_reset_policy=B2C_1_password_reset
```

## Testing

### Unit Tests
Run the Jest test suite:
```bash
npm test tests/forgot-password.test.js
```

### Manual Testing
Use the manual test script:
```bash
node tests/manual-forgot-password-test.js
```

### Test Cases Covered
1. Missing email field validation
2. Invalid email format validation
3. Non-existent email handling (security)
4. Inactive user handling (security)
5. Valid user password reset flow
6. Admin user password reset flow
7. Email service error handling
8. B2C URL generation validation
9. Email template verification

## Error Handling

### Client-Side Errors
- `400 Bad Request`: Invalid input (missing email, invalid format)
- All other scenarios return `200 OK` for security

### Server-Side Error Logging
- Database connection issues
- Email service failures
- B2C service errors
- Audit logging failures

## Monitoring and Logging

### Audit Events
- Password reset requests (successful)
- Failed email deliveries
- Invalid email attempts
- System errors

### Metrics to Monitor
- Password reset request volume
- Email delivery success rate
- B2C redirect success rate
- Failed authentication attempts

## Security Considerations

### Best Practices Implemented
1. **No Email Enumeration**: Always return success message
2. **Input Validation**: Proper email format checking
3. **Audit Logging**: All requests are logged
4. **Secure URLs**: PKCE-enabled B2C URLs
5. **Time-Limited Links**: 24-hour expiration
6. **Professional Templates**: Clear security messaging

### Additional Recommendations
1. Implement rate limiting
2. Monitor for suspicious patterns
3. Regular security audits
4. Keep B2C policies updated
5. Test email deliverability regularly

## Troubleshooting

### Common Issues
1. **Emails not delivered**: Check SMTP configuration
2. **B2C redirect fails**: Verify redirect URLs in B2C policy
3. **Invalid policy errors**: Check B2C policy names
4. **Database connection**: Verify Cosmos DB connection string

### Debug Steps
1. Check Azure Functions logs
2. Verify environment variables
3. Test email service independently
4. Validate B2C policy configuration
5. Check user account status in database

## API Usage Examples

### JavaScript/Fetch
```javascript
const response = await fetch('/auth/forgot-password', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>'
  })
});

const result = await response.json();
console.log(result.message);
```

### cURL
```bash
curl -X POST \
  http://localhost:7071/api/auth/forgot-password \
  -H 'Content-Type: application/json' \
  -d '{"email":"<EMAIL>"}'
```

## Integration Notes

### Frontend Integration
- Show success message regardless of email validity
- Implement client-side email validation for UX
- Handle loading states during API calls
- Provide clear instructions to users

### Backend Integration
- Ensure proper error handling in calling code
- Implement retry logic for email failures
- Monitor API performance and success rates
- Regular testing of B2C integration

/**
 * Manual Test Script for Forgot Password API
 * 
 * This script tests the forgot password functionality by making actual HTTP requests
 * to the Azure Functions endpoint.
 * 
 * Usage:
 * 1. Make sure your Azure Functions app is running locally
 * 2. Update the BASE_URL if needed
 * 3. Run: node tests/manual-forgot-password-test.js
 */

const axios = require('axios')

// Configuration
const BASE_URL = 'http://localhost:7071/api' // Update this to your Azure Functions local URL
const TEST_EMAIL = '<EMAIL>' // Update this to a real email in your system
const INVALID_EMAIL = 'invalid-email-format'
const NON_EXISTENT_EMAIL = '<EMAIL>'

// Test cases
const testCases = [
  {
    name: 'Test 1: Missing email field',
    payload: {},
    expectedStatus: 400,
    expectedMessage: 'Email is required'
  },
  {
    name: 'Test 2: Invalid email format',
    payload: { email: INVALID_EMAIL },
    expectedStatus: 400,
    expectedMessage: 'Invalid email format'
  },
  {
    name: 'Test 3: Non-existent email (should return success for security)',
    payload: { email: NON_EXISTENT_EMAIL },
    expectedStatus: 200,
    expectedMessage: 'If an account with this email exists, you will receive a password reset link shortly.'
  },
  {
    name: 'Test 4: Valid email (should send reset email)',
    payload: { email: TEST_EMAIL },
    expectedStatus: 200,
    expectedMessage: 'If an account with this email exists, you will receive a password reset link shortly.'
  }
]

// Helper function to make API requests
async function makeRequest(payload) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/forgot-password`, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    return {
      status: response.status,
      data: response.data
    }
  } catch (error) {
    if (error.response) {
      return {
        status: error.response.status,
        data: error.response.data
      }
    }
    throw error
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Forgot Password API Tests')
  console.log('=====================================\n')

  let passedTests = 0
  let totalTests = testCases.length

  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}`)
    console.log(`   Payload: ${JSON.stringify(testCase.payload)}`)
    
    try {
      const result = await makeRequest(testCase.payload)
      
      console.log(`   Response Status: ${result.status}`)
      console.log(`   Response Data: ${JSON.stringify(result.data)}`)
      
      // Check status code
      if (result.status === testCase.expectedStatus) {
        console.log('   ✅ Status code matches expected')
        
        // Check message for specific cases
        if (testCase.expectedMessage) {
          const responseMessage = typeof result.data === 'string' ? result.data : result.data.message
          if (responseMessage === testCase.expectedMessage) {
            console.log('   ✅ Response message matches expected')
            passedTests++
          } else {
            console.log(`   ❌ Response message mismatch`)
            console.log(`      Expected: "${testCase.expectedMessage}"`)
            console.log(`      Actual: "${responseMessage}"`)
          }
        } else {
          passedTests++
        }
      } else {
        console.log(`   ❌ Status code mismatch`)
        console.log(`      Expected: ${testCase.expectedStatus}`)
        console.log(`      Actual: ${result.status}`)
      }
      
    } catch (error) {
      console.log(`   ❌ Test failed with error: ${error.message}`)
    }
    
    console.log('')
  }

  // Summary
  console.log('📊 Test Summary')
  console.log('===============')
  console.log(`Passed: ${passedTests}/${totalTests}`)
  console.log(`Failed: ${totalTests - passedTests}/${totalTests}`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!')
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.')
  }
}

// Additional manual tests
async function testB2CUrlGeneration() {
  console.log('\n🔗 Testing B2C URL Generation')
  console.log('==============================')
  
  try {
    // This would require importing the B2C service directly
    // For now, we'll just document what should be tested
    console.log('Manual verification needed:')
    console.log('1. Check that B2C URLs contain proper PKCE parameters')
    console.log('2. Verify redirect URLs are correct for admin vs regular users')
    console.log('3. Confirm login_hint parameter includes the user email')
    console.log('4. Validate that the policy name is correct')
    
  } catch (error) {
    console.log(`❌ B2C URL generation test failed: ${error.message}`)
  }
}

async function testEmailSending() {
  console.log('\n📧 Testing Email Sending')
  console.log('========================')
  
  console.log('Manual verification needed:')
  console.log('1. Check your email inbox for password reset emails')
  console.log('2. Verify the email template looks professional')
  console.log('3. Confirm the reset link works and redirects to B2C')
  console.log('4. Test that security warnings are included in the email')
  console.log('5. Verify different templates for admin vs regular users')
}

// Run all tests
async function main() {
  try {
    await runTests()
    await testB2CUrlGeneration()
    await testEmailSending()
    
    console.log('\n📝 Next Steps:')
    console.log('==============')
    console.log('1. Test with a real email address in your system')
    console.log('2. Verify the B2C password reset flow works end-to-end')
    console.log('3. Check email delivery and template rendering')
    console.log('4. Test with both admin and regular user accounts')
    console.log('5. Verify security measures (no email enumeration)')
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message)
    process.exit(1)
  }
}

// Handle command line execution
if (require.main === module) {
  main()
}

module.exports = {
  runTests,
  testB2CUrlGeneration,
  testEmailSending
}

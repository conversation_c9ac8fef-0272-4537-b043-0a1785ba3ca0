const { app } = require('@azure/functions')
const userHandler = require('../handlers/user-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { DefaultRoles } = require('../common/roles')

app.http('login', {
  methods: ['POST'],
  route: 'auth/login',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      const userData = await userHandler.loginUser(req)

      if (!userData || !userData.body || !userData.body.user) {
        context.log('Invalid user data:', userData)
        return jsonResponse(
          'Invalid login response',
          HttpStatusCode.InternalServerError,
        )
      }

      return jsonResponse(userData.body, HttpStatusCode.Ok)
    } catch (error) {
      context.log('Error during login:', error.message)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('forgot-password', {
  methods: ['POST'],
  route: 'auth/forgot-password',
  authLevel: 'anonymous',
  handler: async (req, context) => {
    try {
      const result = await userHandler.forgotPassword(req)
      return result
    } catch (error) {
      context.log('Error in forgot password:', error.message)
      return jsonResponse(
        {
          message:
            'If an account with this email exists, you will receive a password reset link shortly.',
        },
        HttpStatusCode.Ok,
      )
    }
  },
})

app.http('reset-password', {
  methods: ['POST'],
  route: 'auth/reset-password',
  authLevel: 'function',
  handler: async (req, context) => {
    return userHandler.setPassword(req)
  },
})

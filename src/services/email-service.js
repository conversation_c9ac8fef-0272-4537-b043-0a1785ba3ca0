const nodemailer = require('nodemailer')
const jwt = require('jsonwebtoken')

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
      logger: true,
      debug: true,
    })

    this.transporter.verify((error, success) => {
      if (error) {
        console.error('SMTP configuration error:', error)
      } else {
        console.log('SMTP configuration is correct:', success)
      }
    })
  }

  async sendEmail(to, subject, text, html) {
    try {
      await this.transporter.sendMail({
        from: process.env.EMAIL_USER,
        to,
        subject,
        text,
        html,
      })
    } catch (error) {
      console.error('Error sending email:', error)
      throw new Error('Failed to send email')
    }
  }

  async sendWelcomeEmail(email, resetToken, temporaryPassword = null) {
    const baseAdminUrl = process.env.BASE_ADMIN_URL
    const activationLink = `${baseAdminUrl}/set-password?token=${resetToken}`

    const passwordToShow = temporaryPassword

    const subject = 'Welcome to EMR Platform'
    const text = `Dear User,\n\nWelcome to the EMR Platform! Please activate your account using the following link: ${activationLink}\n\nFor testing purposes, your temporary password is: ${passwordToShow}\n\nIf you have any questions, feel free to contact support.\n\nRegards,\nTeam EMR`
    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
        <h2 style="color: #4CAF50;">Welcome to EMR Platform</h2>
        <p>Dear User,</p>
        <p>Welcome to the EMR Platform! We're excited to have you on board.</p>
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff;">
          <p><strong>For Testing Purposes:</strong></p>
          <p>Your temporary password is: <code style="background-color: #e9ecef; padding: 2px 6px; border-radius: 3px; font-family: monospace;">${passwordToShow}</code></p>
        </div>
        <p>Please activate your account using the button below:</p>
        <div style="text-align: left; margin: 20px 0;">
          <a href="${activationLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;">Activate Account</a>
        </div>
        <p>If you have any questions, feel free to contact support.</p>
        <footer style="margin-top: 20px; font-size: 0.9em; color: #888;">
          <p>Regards,</p>
          <p>Team EMR</p>
        </footer>
      </div>
    `
    await this.sendEmail(email, subject, text, html)
  }

  async sendWelcomeEmailWithResetLink(
    email,
    resetToken,
    organizationName,
    password,
  ) {
    const baseAdminUrl = process.env.BASE_ADMIN_URL
    const resetLink = `${baseAdminUrl}/set-password?token=${resetToken}`
    const subject = `Invitation to Join ${organizationName} on EMR Platform`
    const text = `Dear ${organizationName},\n\nYou have been invited to join the EMR Platform.\n\nYour default password is: ${password}\n\nPlease reset your password using the following link: ${resetLink}\n\nIf you have any trouble accepting the invitation or believe you received this email by mistake, please contact support.\n\nRegards,\nTeam EMR`
    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
        <h2 style="color: #4CAF50;">Invitation to Join ${organizationName}</h2>
        <p>Dear ${organizationName},</p>
        <p>You have been invited to join the EMR Platform.</p>
        <p>Your default password is: <strong>${password}</strong></p>
        <p>Please reset your password using the button below:</p>
        <div style="text-align: left; margin: 20px 0;">
          <a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;">Reset Password</a>
        </div>
        <p>If you have any trouble accepting the invitation or believe you received this email by mistake, please contact support.</p>
        <footer style="margin-top: 20px; font-size: 0.9em; color: #888;">
          <p>Regards,</p>
          <p>Team EMR</p>
        </footer>
      </div>
    `
    await this.sendEmail(email, subject, text, html)
  }

  async generateActivationToken(email) {
    const jwtSecret = process.env.JWT_SECRET
    return jwt.sign({ email }, jwtSecret, { expiresIn: '1d' })
  }

  async generatePasswordResetToken(email) {
    try {
      const jwtSecret = process.env.JWT_SECRET
      return jwt.sign({ email }, jwtSecret, { expiresIn: '1d' })
    } catch (error) {
      console.error('Error generating password reset token:', error)
      throw new Error('Failed to generate password reset token')
    }
  }

  async sendPasswordResetEmailWithB2C(
    email,
    name,
    b2cResetUrl,
    isAdmin = false,
  ) {
    const subject = 'Password Reset - EMR Platform'
    const roleText = isAdmin ? 'Administrator' : 'User'
    const platformText = isAdmin ? 'Admin Portal' : 'EMR Platform'

    const text = `Dear ${name},\n\nWe received a request to reset your password for your ${roleText} account on the EMR Platform.\n\nTo reset your password, please click the link below:\n${b2cResetUrl}\n\nThis link will take you to a secure page where you can create a new password for your account.\n\nImportant Security Information:\n- This link will expire in 24 hours for security reasons\n- If you didn't request this password reset, please ignore this email\n- Your account remains secure and no changes have been made\n\nIf you continue to have trouble accessing your account, please contact our support team.\n\nRegards,\nTeam EMR`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #4CAF50; margin: 0; font-size: 28px;">EMR Platform</h1>
          <p style="color: #666; margin: 5px 0 0 0; font-size: 14px;">Electronic Medical Records</p>
        </div>

        <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px;">Password Reset Request</h2>
          <p style="margin: 0 0 15px 0; font-size: 16px;">Dear <strong>${name}</strong>,</p>
          <p style="margin: 0 0 20px 0; font-size: 16px;">We received a request to reset your password for your <strong>${roleText}</strong> account on the EMR Platform.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${b2cResetUrl}" style="background-color: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: bold; display: inline-block; box-shadow: 0 3px 6px rgba(0,0,0,0.1);">Reset Your Password</a>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>🔒 Security Notice:</strong></p>
            <ul style="margin: 10px 0 0 0; color: #856404; padding-left: 20px;">
              <li>This link will expire in 24 hours for security reasons</li>
              <li>If you didn't request this password reset, please ignore this email</li>
              <li>Your account remains secure and no changes have been made</li>
            </ul>
          </div>

          <p style="margin: 20px 0 0 0; font-size: 14px; color: #666;">If the button above doesn't work, you can copy and paste this link into your browser:</p>
          <p style="margin: 5px 0 0 0; font-size: 12px; color: #666; word-break: break-all; background-color: #f1f1f1; padding: 10px; border-radius: 4px;">${b2cResetUrl}</p>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="margin: 0; font-size: 14px; color: #666;">If you continue to have trouble accessing your account, please contact our support team.</p>
          <p style="margin: 15px 0 0 0; font-size: 14px; color: #666;">
            <strong>Team EMR</strong><br>
            Electronic Medical Records Platform
          </p>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }

  async sendWelcomeEmailWithB2CCredentials(
    email,
    name,
    temporaryPassword,
    b2cResetUrl,
    isAdmin = false,
  ) {
    const subject = isAdmin
      ? 'Welcome to EMR Platform - Organization Admin Account Created'
      : 'Welcome to EMR Platform - Account Created'

    const roleText = isAdmin ? 'organization administrator' : 'team member'

    const platformText = isAdmin ? 'Admin Portal' : 'EMR Platform'

    const text = `Dear ${name},\n\nWelcome to the EMR Platform! Your ${roleText} account has been successfully created.\n\nYour login credentials:\nEmail: ${email}\nTemporary Password: ${temporaryPassword}\n\nIMPORTANT: For security reasons, you must change your password on first login.\n\nTo access the ${platformText} and set your permanent password, please visit: ${b2cResetUrl}\n\nWhat happens next:\n1. Visit the login portal using the link above\n2. Sign in with your email and temporary password\n3. You'll be prompted to create a new secure password\n4. Once completed, you can access the ${platformText}\n\nIf you have any questions, feel free to contact support.\n\nRegards,\nTeam EMR`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333; max-width: 600px; margin: 0 auto;">
       <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">

          <h1 style="margin: 0; font-size: 24px;">${
            isAdmin
              ? '🏢 Welcome to EMR Admin Portal!'
              : '👋 Welcome to EMR Platform!'
          }</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; margin-bottom: 20px;">Dear <strong>${name}</strong>,</p>

          <p>Great news! Your <strong>${roleText}</strong> account has been successfully created on the EMR Platform.</p>
          ${
            isAdmin
              ? `
            <div style="background-color: #e8f5e9; border-left: 4px solid #4CAF50; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #2e7d32;"><strong>🏢 Administrator Role</strong></p>
              <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">
                You’ve been assigned as an administrator. You can manage your organization’s settings, users, and data.
              </p>
            </div>
            `
              : `
            <div style="background-color: #e8f5e9; border-left: 4px solid #4CAF50; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #2e7d32;"><strong>👥 Team Member Role</strong></p>
              <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">
                You’ve been added as a team member. You can now use the EHR platform to support patient care and collaborate with your organization.
              </p>
            </div>
            `
          }
          
          <div style="background-color: #fff; border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #4CAF50; margin-top: 0;">🔑 Your Login Credentials</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
                <td style="padding: 8px 0; font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px;">${email}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Temporary Password:</td>
                <td style="padding: 8px 0; font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px; font-weight: bold; color: #d32f2f;">${temporaryPassword}</td>
              </tr>
            </table>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>⚠️ IMPORTANT:</strong> For security reasons, you must change your password on first login.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${b2cResetUrl}" style="background-color: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: bold; display: inline-block; box-shadow: 0 3px 6px rgba(0,0,0,0.1);">Access ${platformText}</a>
          </div>

          <div style="background-color: #e3f2fd; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <h4 style="color: #1976D2; margin-top: 0;">📋 What happens next?</h4>
            <ol style="margin: 10px 0; padding-left: 20px; color: #555;">
              <li>Click the "Access ${platformText}" button above</li>
              <li>You'll be taken to the secure Microsoft B2C login page</li>
              <li>Enter your email: <strong>${email}</strong></li>
              <li>Enter the temporary password shown above</li>
              <li>Microsoft will prompt you to create a new secure password</li>
              <li>After setting your password, you'll be redirected to the ${platformText}</li>
              <li>Use your email and new password for all future logins</li>
            </ol>
          </div>

          <div style="background-color: #e8f5e8; border: 1px solid #4CAF50; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #2e7d32;"><strong>🔐 Secure OAuth Authentication</strong></p>
            <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">This link uses enterprise-grade OAuth 2.0 with PKCE security for maximum protection.</p>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>💡 Important Notes:</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px; color: #856404;">
              <li>The temporary password is only valid for first-time login</li>
              <li>You must create a new password during your first login</li>
              <li>All password management is handled securely by Microsoft</li>
              <li>This link uses the same secure OAuth flow as your regular login</li>
              <li>If you encounter any issues, try refreshing the page</li>
            </ul>
          </div>

          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              <strong>Having trouble with the button?</strong><br>
              Copy and paste this link into your browser:
            </p>
            <p style="word-break: break-all; color: #007bff; font-family: monospace; font-size: 12px; background-color: #fff; padding: 8px; border-radius: 4px; margin: 10px 0 0 0;">${b2cResetUrl}</p>
          </div>

          <p style="margin-top: 30px;">If you have any questions or need assistance, feel free to contact our support team.</p>

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; text-align: center; color: #888;">
            <p style="margin: 0; font-weight: bold;">Best regards,</p>
            <p style="margin: 5px 0 15px 0; font-weight: bold;">Team EMR</p>
            <p style="font-size: 12px; margin: 0;">This is an automated message. Please do not reply to this email.</p>
          </footer>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }
}

module.exports = new EmailService()
